import { cartProvider } from 'App/Providers/CartProvider'
import { FindByCategoryIdParamsSchema, GlobalSearchParamsSchema, productProvider } from 'App/Providers/ProductProvider'
// import { publicProcedure, router } from 'App/Services/tRPC'
import { CreateRouterParams } from 'App/Services/tRPC'

import { z } from 'zod'

export const productRouter = ({ router, publicProcedure, authedProcedure }: CreateRouterParams) =>
  router({
    //! ------------ CART ------------
    checkCookie: publicProcedure.query(async ({ ctx, input }) => {
      return await cartProvider.checkCookie(ctx.ctx)
    }),
    getCartCount: publicProcedure.query(async ({ ctx, input }) => {
      let sessionUser = ctx.sessionUser
      const cartId = await cartProvider.getCartId({ ctx: ctx.ctx, sessionUser })

      return await cartProvider.getCartCount(cartId)
    }),
    getCartItems: publicProcedure.query(async ({ ctx, input }) => {
      let sessionUser = ctx.sessionUser
      const cartId = await cartProvider.getCartId({ ctx: ctx.ctx, sessionUser })

      return await cartProvider.getCartItems({ cartId })
    }),
    getCartSum: publicProcedure.query(async ({ ctx, input }) => {
      let sessionUser = ctx.sessionUser
      const cartId = await cartProvider.getCartId({ ctx: ctx.ctx, sessionUser })

      // return await cartProvider.getCartItems({ cartId })

      return await cartProvider.getCartSum({ cartId })
    }),
    getCartProducts: publicProcedure
      .input(
        z.object({
          page: z.number().optional(),
          sorting: z
            .array(
              z.object({
                column: z.string(),
                direction: z.enum(['asc', 'desc'])
              })
            )
            .optional()
        })
      )
      .query(async ({ ctx, input }) => {
        let sessionUser = ctx.sessionUser

        const cartId = await cartProvider.getCartId({ ctx: ctx.ctx, sessionUser })

        return await cartProvider.getCartProducts({ cartId: cartId || 0, page: input.page, sorting: input.sorting })
      }),
    pushToCart: publicProcedure.input(z.object({ prodId: z.number(), qty: z.number() })).mutation(async ({ ctx, input }) => {
      let sessionUser = ctx.sessionUser
      const cartId = await cartProvider.getCartId({ ctx: ctx.ctx, sessionUser, autoCreate: true })
      // console.log('🚀 ~ pushToCart:publicProcedure.input ~ cartId:', cartId)

      const cartItem = await cartProvider.push(cartId, input)

      return cartItem
    }),
    //! ---------- CATALOG -----------
    getFiltersData: publicProcedure
      .input(
        z.object({
          identifier: z
            .string()
            .or(z.array(z.string().or(z.number())))
            .or(z.number()),
          search: z.string().max(150).optional().nullable(),
          filters: z.any().optional().nullable()
        })
      )
      .query(async ({ ctx, input }) => {
        return await productProvider.filterDataByIdentifier({ ...input })
      }),
    getFiltersDataMeili: publicProcedure
      .input(
        z.object({
          identifier: z
            .string()
            .or(z.array(z.string().or(z.number())))
            .or(z.number()),
          search: z.string().max(150).optional().nullable(),
          filters: z.any().optional().nullable()
        })
      )
      .query(async ({ ctx, input }) => {
        return await productProvider.filterDataByIdentifierMeili({ ...input })
      }),
    getRootCategories: publicProcedure.query(async ({ ctx, input }) => {
      return await productProvider.getRootCategories()
    }),
    getCategories: publicProcedure.query(async ({ ctx, input }) => {
      return await productProvider.getCategories()
    }),
    globalSearch: publicProcedure.input(GlobalSearchParamsSchema).query(async ({ ctx, input }) => {
      return await productProvider.globalSearch(input, ctx.ctx.request)
    }),
    globalSearchMeili: publicProcedure.input(GlobalSearchParamsSchema).query(async ({ ctx, input }) => {
      return await productProvider.globalSearchMeili(input, ctx.ctx.request)
    }),
    globalFlatSearchMeili: publicProcedure.input(GlobalSearchParamsSchema).query(async ({ ctx, input }) => {
      return await productProvider.globalFlatSearchMeili(input)
    }),
    getCategoryProduct: publicProcedure.input(FindByCategoryIdParamsSchema).query(async ({ ctx, input }) => {
      return await productProvider.findByCategoryIdOrUrl(input)
    }),
    getCategoryProductMeili: publicProcedure.input(FindByCategoryIdParamsSchema).query(async ({ ctx, input }) => {
      return await productProvider.findByCategoryIdOrUrlWithMeili(input)
    }),
    getRkProducts: publicProcedure.input(z.number()).query(async ({ ctx, input }) => {
      return await productProvider.getRkProducts({ prod_id: input })
    }),

    getProductById: publicProcedure.input(z.number()).query(async ({ ctx, input }) => {
      return await productProvider.getProductById(input, ctx.ctx?.request)
    }),
    getProductByIdMeili: publicProcedure.input(z.number()).query(async ({ ctx, input }) => {
      return await productProvider.getProductByIdMeili(input, ctx.ctx?.request)
    }),
    getProductDataById: publicProcedure.input(z.number()).query(async ({ ctx, input }) => {
      return await productProvider.getProductDataById(input)
    }),

    getProductSchemaByProductId: publicProcedure.input(z.number()).query(async ({ ctx, input }) => {
      return await productProvider.getProductSchemaByProductId(input)
    }),
    getProductBreadcrumbs: publicProcedure.input(z.number()).query(async ({ ctx, input }) => {
      return await productProvider.getProductBreadcrumbs(input)
    }),

    getSubCategories: publicProcedure.input(z.number().or(z.string().min(1).max(100))).query(async ({ ctx, input }) => {
      return await productProvider.getSubCategories(input)
    })
  })

// as ReturnType<typeof router>

// export type ProductRouter =  ReturnType<typeof productRouter>

import { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'
import Env from '@ioc:Adonis/Core/Env'
import Mail from '@ioc:Adonis/Addons/Mail'
import { randomInteger } from 'App/Helpers/randomInteger'
import Client from 'App/Models/Client'
import Org from 'App/Models/Org'
import Order from 'App/Models/Order'
import i18next from 'i18next'
import User from 'App/Models/User'
import Hash from '@ioc:Adonis/Core/Hash'
import Journal from 'App/Models/Journal'
import loadSettings from 'App/Helpers/loadSettings'
import Application from '@ioc:Adonis/Core/Application'

export default class AuthController {
  public async apilogin({ auth, request, response, session }: HttpContextContract) {
    const { login, password, code } = request.all()

    console.log('AuthController ~ apilogin ~ code:', code)

    const user = await User.findBy('user_name', login)

    if (!user) {
      return response.status(404).send(i18next.t('Клиент с такими данными не найден'))
    }

    // if (!Application.inProduction) {
    //   return await auth.use('api').attempt(login, password, {
    //     expiresIn: '365days',
    //     ip_address: String(request.ip())
    //   })
    // }

    if (!code) {
      const { service_mail } = await loadSettings(['service_mail'])
      // Add two-factor authentication code here
      session.put('2faCode', randomInteger(1000, 9999))
      const emailCode = session.get('2faCode')

      const EMAIL_FROM = Env.get('EMAIL_FROM')
      const userEmail = service_mail || EMAIL_FROM

      if (Application.inProduction) {
        await Mail.send((message) => {
          message
            .from(EMAIL_FROM, Env.get('EMAIL_FROM_NAME'))
            .to(userEmail)
            .subject(i18next.t('Код для входа'))
            .text(`${i18next.t('Код для входа')}: "${emailCode}" | ${user.user_name} | ${request.ip()}`)
        })
      } else {
        console.log('cpan login requestcode:', emailCode)
      }

      return { msg: i18next.t('Введите код подтверждения из e-mail'), requestcode: true }
    } else {
      if (session.get('2faCode') == code) {
        try {
          const token = await auth.use('api').attempt(login, password, {
            expiresIn: '365days',
            ip_address: String(request.ip())
          })

          try {
            Journal.createItem({
              entity: 'users',
              entity_id: 0,
              msg: 'логин в панель управления'
            })
          } catch (error) {}

          session.forget('2faCode')

          return token
        } catch (e) {
          console.log('apilogin e:', e)
          return response.badRequest('Invalid credentials')
        }
      } else {
        return response.badRequest('Invalid 2faCode')
      }
    }
  }

  public async login({ auth, request, response }) {
    const { login, password } = request.requestBody
    const locale = request.headers()['x-locale'] || undefined
    console.log("🚀 ~ AuthController ~ login ~ login:", login)

    i18next.changeLanguage(locale || 'ru')

    // const client = await Client.query().where('client_number', login).orWhere('client_mail', login).preload('org').preload('orders').first()
    // console.log('client', client)

    let client = await Client.findBy('client_number', login)

    if (!client) {
      client = await Client.findBy('client_mail', login)
    }

    if (!client) {
      return response.status(404)//.send(i18next.t('Клиент с такими данными не найден'))
    }

    if (!client.password) {
      return { emptypassword: i18next.t('Пожалуйста, установите пароль') }
    }

    try {
      const user = await auth.attempt(client.client_number, password)
      const org = await Org.query().where('org_client', user.client_number).first()
      if (org) {
        user.org = org
      }
      return user
    } catch (error) {
      //console.warn('error login: ', error)
      return response.status(401).send(i18next.t('Клиент с такими данными не найден'))
    }
  }

  public async acheck({ auth, request, response }) {
    try {
      const user: User = await auth.use('api').authenticate()
      // console.log('api user', user)

      // Journal.createItem({
      //     entity: 'users',
      //     entity_id: user.user_id,
      //     msg: 'авториация в панель управления'
      //   })

      return user
    } catch (error) {
      return response.status(401)
    }
  }

  public async check({ auth, request, response }) {
    try {
      const user = await auth.authenticate()
      const org = await Org.query().where('org_client', user.client_number).first()

      try {
        Journal.createItem({
          entity: 'users',
          entity_id: user.user_id,
          msg: 'авториация на сайт'
        })
      } catch (error) {}

      if (org) {
        user.org = org
      }

      return user
    } catch (error) {
      return response.status(401)
    }
  }

  public async resetpassword({ request, params }) {
    const EMAIL_FROM = Env.get('EMAIL_FROM')
    const locale = request.headers()['x-locale'] || undefined
    const isRumisota = locale && locale != 'ru'

    i18next.changeLanguage(locale || 'ru')

    let { email } = params
    const ip = request.ip()

    if (!email) {
      return { error: i18next.t('Пользователь не найден') }
    }

    email = decodeURIComponent(email)
    console.log('🚀 ~ AuthController ~ resetpassword ~ email:', email)

    let client = await Client.findBy('client_mail', email)

    if (!client) {
      client = await Client.findBy('client_number', email)
    }
    // let client = await Client.query().where('client_mail', email).orWhere('client_number', email).firstOrFail()

    if (!client) {
      return { error: i18next.t('Пользователь не найден') }
    }

    let _pwd = randomInteger(10000, 999999)
    client.password = _pwd
    await client.save()

    return { msg: i18next.t('Данные высланы на вашу электронную почту') }
  }

  public async setPassword({ auth, request, response, session }) {
    const { login, password, confirmСode } = request.requestBody

    const locale = request.headers()['x-locale'] || undefined
    const isRumisota = locale && locale != 'ru'

    i18next.changeLanguage(locale || 'ru')

    let client = await Client.findBy('client_number', login)

    if (!client) {
      client = await Client.findBy('client_mail', login)
    }

    if (!client) {
      return response.status(404).send(i18next.t('Клиент с такими данными не найден'))
    }

    async function sendCode(s = false) {
      session.put('_fpwd', randomInteger(1000, 9999))

      const msg = `${i18next.t('Код для смены пароля')}: ${session.get('_fpwd')}`

      let mailer = !isRumisota ? 'smtp' : 'rumi'

      await Mail.use(mailer).send((message) => {
        message
          .from(!isRumisota ? Env.get('EMAIL_FROM') : Env.get('RUMI_EMAIL_FROM', '<EMAIL>'))
          .to(client?.client_mail)
          .subject(i18next.t('Код для смены пароля'))
          .text(msg)
      })

      if (s) {
        response.status(200).send({ msg: i18next.t('Введите код подтверждения из e-mail'), requestcode: true })
      }
    }

    if (confirmСode && confirmСode !== '') {
      //console.log('confirmСode', confirmСode)
      //console.log('session.get(\'_fpwd\')', session.get('_fpwd'))
      if (confirmСode == session.get('_fpwd')) {
        try {
          client.password = password
          await client.save()

          return { msg: i18next.t('Данные высланы на вашу электронную почту') }
        } catch (error) {
          console.error('AuthController > setPassword: ', error)
          return response.status(400).send('Произошла ошибка, попробуйте позже')
        }
      } else {
        return { error: i18next.t('Неверно введен код') }
      }
    } else {
      await sendCode(true)
    }
  }

  public async logout({ auth, response, request }: HttpContextContract) {
    await auth.logout()

    try {
      await auth.use('api').authenticate()
      await auth.use('api').revoke()
      auth.use('api').logout()
    } catch (error) {
      return response.status(500)
    }

    response.clearCookie('rti-session')

    return response.status(200)
  }
}
